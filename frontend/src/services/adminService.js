import { apiClient, API_ENDPOINTS } from '../config/api.js';

export const adminService = {
  // Authentication
  async login(username, password) {
    try {
      const response = await apiClient.post(API_ENDPOINTS.ADMIN_LOGIN, {
        username,
        password,
      });
      return { success: true, admin: response.admin };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async logout() {
    try {
      await apiClient.post('/admin/logout');
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async getProfile() {
    try {
      const response = await apiClient.get('/admin/profile');
      return { success: true, admin: response.admin };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // Dashboard Statistics
  async getDashboardStats() {
    try {
      const response = await apiClient.get('/admin/dashboard-stats');
      return { success: true, stats: response.stats };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // Bus Management
  async getAllBuses() {
    try {
      const response = await apiClient.get('/buses');
      return { success: true, buses: response.buses };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async getBusById(id) {
    try {
      const response = await apiClient.get(`/buses/${id}`);
      return { success: true, bus: response.bus };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async createBus(busData) {
    try {
      const response = await apiClient.post('/buses', busData);
      return { success: true, bus: response.bus };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async updateBus(id, busData) {
    try {
      const response = await apiClient.put(`/buses/${id}`, busData);
      return { success: true, bus: response.bus };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async deleteBus(id) {
    try {
      await apiClient.delete(`/buses/${id}`);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // Route Management
  async getAllRoutes() {
    try {
      const response = await apiClient.get('/routes');
      return { success: true, routes: response.routes };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async createRoute(routeData) {
    try {
      const response = await apiClient.post('/routes', routeData);
      return { success: true, route: response.route };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async updateRoute(id, routeData) {
    try {
      const response = await apiClient.put(`/routes/${id}`, routeData);
      return { success: true, route: response.route };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async deleteRoute(id) {
    try {
      await apiClient.delete(`/routes/${id}`);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // Schedule Management
  async getAllSchedules() {
    try {
      const response = await apiClient.get('/schedules');
      return { success: true, schedules: response.schedules };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async createSchedule(scheduleData) {
    try {
      const response = await apiClient.post('/schedules', scheduleData);
      return { success: true, schedule: response.schedule };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async updateSchedule(id, scheduleData) {
    try {
      const response = await apiClient.put(`/schedules/${id}`, scheduleData);
      return { success: true, schedule: response.schedule };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async deleteSchedule(id) {
    try {
      await apiClient.delete(`/schedules/${id}`);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // Booking Management
  async getAllBookings() {
    try {
      const response = await apiClient.get('/bookings');
      return { success: true, bookings: response.bookings };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async getBookingsByDateRange(startDate, endDate) {
    try {
      const response = await apiClient.get(`/bookings/date-range?start=${startDate}&end=${endDate}`);
      return { success: true, bookings: response.bookings };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // User Management
  async getAllUsers() {
    try {
      const response = await apiClient.get('/admin/users');
      return { success: true, users: response.users };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // Maintenance Management
  async getAllMaintenance() {
    try {
      const response = await apiClient.get('/maintenance');
      return { success: true, maintenance: response.maintenance };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async createMaintenance(maintenanceData) {
    try {
      const response = await apiClient.post('/maintenance', maintenanceData);
      return { success: true, maintenance: response.maintenance };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async updateMaintenance(id, maintenanceData) {
    try {
      const response = await apiClient.put(`/maintenance/${id}`, maintenanceData);
      return { success: true, maintenance: response.maintenance };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // Feedback Management
  async getAllFeedback() {
    try {
      const response = await apiClient.get('/feedback');
      return { success: true, feedback: response.feedback };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async getFeedbackStatistics() {
    try {
      const response = await apiClient.get('/feedback/statistics');
      return { success: true, statistics: response.statistics };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // Refund Management
  async getAllRefunds() {
    try {
      const response = await apiClient.get('/refunds');
      return { success: true, refunds: response.refunds };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async updateRefundStatus(id, status) {
    try {
      const response = await apiClient.put(`/refunds/${id}/status`, { status });
      return { success: true, refund: response.refund };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },
};

export default adminService;

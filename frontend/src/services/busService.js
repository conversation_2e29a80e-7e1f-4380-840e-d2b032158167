import { apiClient, API_ENDPOINTS } from '../config/api.js';

export const busService = {
  // Search buses
  async searchBuses(searchData) {
    try {
      const requestData = {
        source: searchData.source,
        destination: searchData.destination,
        journeyDate: searchData.date,
      };

      const response = await apiClient.post(API_ENDPOINTS.SCHEDULES_SEARCH, requestData);

      return { success: true, buses: response.schedules || [] };
    } catch (error) {
      console.error('Bus search error:', error);
      return { success: false, error: error.message, buses: [] };
    }
  },

  // Get all buses
  async getAllBuses() {
    try {
      const response = await apiClient.get(API_ENDPOINTS.BUSES_LIST);
      return { success: true, buses: response.buses || [] };
    } catch (error) {
      return { success: false, error: error.message, buses: [] };
    }
  },

  // Get all routes
  async getAllRoutes() {
    try {
      const response = await apiClient.get(API_ENDPOINTS.ROUTES_LIST);
      return { success: true, routes: response.routes || [] };
    } catch (error) {
      return { success: false, error: error.message, routes: [] };
    }
  },

  // Get schedules
  async getSchedules() {
    try {
      const response = await apiClient.get(API_ENDPOINTS.SCHEDULES_LIST);
      return { success: true, schedules: response.schedules || [] };
    } catch (error) {
      return { success: false, error: error.message, schedules: [] };
    }
  },

  // Get available locations
  async getLocations() {
    try {
      const response = await apiClient.get(API_ENDPOINTS.ROUTES_LIST);
      if (response.success) {
        // Extract unique locations from routes
        const locations = new Set();
        response.routes.forEach(route => {
          locations.add(route.source);
          locations.add(route.destination);
        });
        return { success: true, locations: Array.from(locations).sort() };
      }
      return { success: false, error: 'Failed to fetch routes', locations: [] };
    } catch (error) {
      return { success: false, error: error.message, locations: [] };
    }
  },
};

export default busService;

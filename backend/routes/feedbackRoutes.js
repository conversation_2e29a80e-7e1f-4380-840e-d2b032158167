const express = require('express');
const router = express.Router();
const feedbackController = require('../controllers/feedbackController');
const { isUserLoggedIn, isAdminLoggedIn } = require('../middleware/auth');

// User routes
router.post('/', isUserLoggedIn, feedbackController.createFeedback);
router.get('/user', isUserLoggedIn, feedbackController.getFeedbackByUserId);

// Admin routes
router.get('/', isAdminLoggedIn, feedbackController.getAllFeedback);
router.get('/statistics', isAdminLoggedIn, feedbackController.getFeedbackStatistics);

module.exports = router;
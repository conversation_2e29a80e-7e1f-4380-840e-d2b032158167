const express = require('express');
const router = express.Router();
const maintenanceController = require('../controllers/maintenanceController');
const { isAdminLoggedIn } = require('../middleware/auth');

// Admin routes
router.get('/', isAdminLoggedIn, maintenanceController.getAllMaintenance);
router.get('/:id', isAdminLoggedIn, maintenanceController.getMaintenanceById);
router.post('/', isAdminLoggedIn, maintenanceController.createMaintenance);
router.put('/:id', isAdminLoggedIn, maintenanceController.updateMaintenance);
router.delete('/:id', isAdminLoggedIn, maintenanceController.deleteMaintenance);

module.exports = router;
const Maintenance = require('../models/maintenanceModel');

// Get all maintenance records
const getAllMaintenance = async (req, res) => {
  try {
    const records = await Maintenance.getAll();
    res.status(200).json({ records });
  } catch (error) {
    console.error('Error getting maintenance records:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get maintenance by ID
const getMaintenanceById = async (req, res) => {
  try {
    const maintenanceId = req.params.id;
    
    const record = await Maintenance.getById(maintenanceId);
    
    if (!record) {
      return res.status(404).json({ message: 'Maintenance record not found' });
    }
    
    res.status(200).json({ record });
  } catch (error) {
    console.error('Error getting maintenance record:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create new maintenance record
const createMaintenance = async (req, res) => {
  try {
    const { busId, maintenanceType, description, scheduledDate } = req.body;
    
    // Validate input
    if (!busId || !maintenanceType || !scheduledDate) {
      return res.status(400).json({ message: 'Bus ID, maintenance type, and scheduled date are required' });
    }
    
    const record = await Maintenance.create(busId, maintenanceType, description, scheduledDate);
    
    res.status(201).json({
      message: 'Maintenance record created successfully',
      record
    });
  } catch (error) {
    console.error('Error creating maintenance record:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update maintenance record
const updateMaintenance = async (req, res) => {
  try {
    const maintenanceId = req.params.id;
    const { status, completionDate, cost } = req.body;
    
    // Validate input
    if (!status) {
      return res.status(400).json({ message: 'Status is required' });
    }
    
    // Validate status
    if (!['Scheduled', 'In Progress', 'Completed'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }
    
    // If status is Completed, completion date is required
    if (status === 'Completed' && !completionDate) {
      return res.status(400).json({ message: 'Completion date is required for completed maintenance' });
    }
    
    const record = await Maintenance.update(maintenanceId, status, completionDate, cost);
    
    res.status(200).json({
      message: 'Maintenance record updated successfully',
      record
    });
  } catch (error) {
    console.error('Error updating maintenance record:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete maintenance record
const deleteMaintenance = async (req, res) => {
  try {
    const maintenanceId = req.params.id;
    
    await Maintenance.delete(maintenanceId);
    
    res.status(200).json({
      message: 'Maintenance record deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting maintenance record:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getAllMaintenance,
  getMaintenanceById,
  createMaintenance,
  updateMaintenance,
  deleteMaintenance
};
const Feedback = require('../models/feedbackModel');

// Create new feedback
const createFeedback = async (req, res) => {
  try {
    const userId = req.session.userId;
    const { bookingId, rating, comments } = req.body;
    
    // Validate input
    if (!rating) {
      return res.status(400).json({ message: 'Rating is required' });
    }
    
    // Validate rating range
    if (rating < 1 || rating > 5) {
      return res.status(400).json({ message: 'Rating must be between 1 and 5' });
    }
    
    const feedback = await Feedback.create(userId, bookingId, rating, comments);
    
    res.status(201).json({
      message: 'Feedback submitted successfully',
      feedback
    });
  } catch (error) {
    console.error('Error creating feedback:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all feedback (admin)
const getAllFeedback = async (req, res) => {
  try {
    const feedback = await Feedback.getAll();
    
    res.status(200).json({ feedback });
  } catch (error) {
    console.error('Error getting all feedback:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get feedback by user ID
const getFeedbackByUserId = async (req, res) => {
  try {
    const userId = req.session.userId;
    
    const feedback = await Feedback.getByUserId(userId);
    
    res.status(200).json({ feedback });
  } catch (error) {
    console.error('Error getting feedback by user ID:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get feedback statistics (admin)
const getFeedbackStatistics = async (req, res) => {
  try {
    const statistics = await Feedback.getStatistics();
    
    res.status(200).json({ statistics });
  } catch (error) {
    console.error('Error getting feedback statistics:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  createFeedback,
  getAllFeedback,
  getFeedbackByUserId,
  getFeedbackStatistics
};
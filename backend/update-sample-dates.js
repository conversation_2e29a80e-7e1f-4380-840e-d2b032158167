const mysql = require('mysql2/promise');
require('dotenv').config();

async function updateSampleDates() {
  let connection;

  try {
    console.log('🚀 Updating sample data with current dates...');

    // Connect to the database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || 'password',
      database: process.env.DB_NAME || 'bus_reservation_system'
    });

    console.log('✅ Connected to database');

    // Get current date and future dates
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    const dayAfterTomorrow = new Date(today);
    dayAfterTomorrow.setDate(today.getDate() + 2);
    
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);

    // Format dates for MySQL
    const formatDate = (date) => date.toISOString().split('T')[0];
    
    // Update schedules with current dates
    const scheduleUpdates = [
      // Today's schedules
      {
        id: 1,
        departure: `${formatDate(today)} 08:00:00`,
        arrival: `${formatDate(today)} 12:30:00`
      },
      {
        id: 2,
        departure: `${formatDate(today)} 09:00:00`,
        arrival: `${formatDate(today)} 13:15:00`
      },
      {
        id: 3,
        departure: `${formatDate(today)} 10:00:00`,
        arrival: `${formatDate(today)} 15:30:00`
      },
      {
        id: 4,
        departure: `${formatDate(today)} 11:00:00`,
        arrival: `${formatDate(today)} 17:45:00`
      },
      {
        id: 5,
        departure: `${formatDate(today)} 12:00:00`,
        arrival: `${formatDate(today)} 16:30:00`
      },
      // Tomorrow's schedules
      {
        id: 6,
        departure: `${formatDate(tomorrow)} 08:00:00`,
        arrival: `${formatDate(tomorrow)} 12:30:00`
      },
      {
        id: 7,
        departure: `${formatDate(tomorrow)} 09:00:00`,
        arrival: `${formatDate(tomorrow)} 13:15:00`
      },
      {
        id: 8,
        departure: `${formatDate(tomorrow)} 10:00:00`,
        arrival: `${formatDate(tomorrow)} 15:30:00`
      },
      // Future schedules
      {
        id: 9,
        departure: `${formatDate(dayAfterTomorrow)} 11:00:00`,
        arrival: `${formatDate(dayAfterTomorrow)} 17:45:00`
      },
      {
        id: 10,
        departure: `${formatDate(nextWeek)} 12:00:00`,
        arrival: `${formatDate(nextWeek)} 16:30:00`
      }
    ];

    // Update each schedule
    for (const schedule of scheduleUpdates) {
      await connection.execute(
        'UPDATE bus_schedules SET departure_time = ?, arrival_time = ? WHERE schedule_id = ?',
        [schedule.departure, schedule.arrival, schedule.id]
      );
    }

    console.log('✅ Updated bus schedules with current dates');

    // Update booking dates to match
    await connection.execute(
      'UPDATE bookings SET journey_date = ? WHERE booking_id IN (1, 2, 3)',
      [formatDate(today)]
    );

    await connection.execute(
      'UPDATE bookings SET journey_date = ? WHERE booking_id = 4',
      [formatDate(tomorrow)]
    );

    console.log('✅ Updated booking dates');

    console.log('🎉 Sample data dates updated successfully!');
    console.log('');
    console.log('📅 Available routes for today:');
    console.log(`   - New York → Washington DC (${formatDate(today)})`);
    console.log(`   - Boston → New York (${formatDate(today)})`);
    console.log(`   - Chicago → Detroit (${formatDate(today)})`);
    console.log(`   - Los Angeles → San Francisco (${formatDate(today)})`);
    console.log(`   - Miami → Orlando (${formatDate(today)})`);
    console.log('');

  } catch (error) {
    console.error('❌ Error updating sample dates:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run update if this file is executed directly
if (require.main === module) {
  updateSampleDates();
}

module.exports = updateSampleDates;

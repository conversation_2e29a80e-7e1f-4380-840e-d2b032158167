const db = require('../db/connection');

class Bus {
  // Get all buses
  static async getAll() {
    try {
      const [buses] = await db.execute('SELECT * FROM buses');
      return buses;
    } catch (error) {
      throw error;
    }
  }
  
  // Get bus by ID
  static async getById(busId) {
    try {
      const [buses] = await db.execute('SELECT * FROM buses WHERE bus_id = ?', [busId]);
      
      if (buses.length === 0) {
        return null;
      }
      
      return buses[0];
    } catch (error) {
      throw error;
    }
  }
  
  // Create new bus
  static async create(busNumber, busType, totalSeats, amenities) {
    try {
      const [result] = await db.execute(
        'INSERT INTO buses (bus_number, bus_type, total_seats, amenities) VALUES (?, ?, ?, ?)',
        [busNumber, busType, totalSeats, amenities]
      );
      
      return { busId: result.insertId, busNumber, busType, totalSeats, amenities };
    } catch (error) {
      throw error;
    }
  }
  
  // Update bus
  static async update(busId, busType, totalSeats, amenities, status) {
    try {
      await db.execute(
        'UPDATE buses SET bus_type = ?, total_seats = ?, amenities = ?, status = ? WHERE bus_id = ?',
        [busType, totalSeats, amenities, status, busId]
      );
      
      return { busId, busType, totalSeats, amenities, status };
    } catch (error) {
      throw error;
    }
  }
  
  // Delete bus
  static async delete(busId) {
    try {
      await db.execute('DELETE FROM buses WHERE bus_id = ?', [busId]);
      return true;
    } catch (error) {
      throw error;
    }
  }
  
  // Search for available buses with improved algorithm
  static async searchAvailable(source, destination, date) {
    try {
      const [buses] = await db.execute(`
        SELECT bs.schedule_id, b.bus_id, b.bus_number, b.bus_type, b.amenities,
               r.source, r.destination, r.distance,
               bs.departure_time, bs.arrival_time, bs.fare, bs.available_seats,
               d.full_name as driver_name,
               CASE
                 WHEN b.status = 'Maintenance' THEN 0
                 ELSE 1
               END as is_available
        FROM bus_schedules bs
        JOIN buses b ON bs.bus_id = b.bus_id
        JOIN routes r ON bs.route_id = r.route_id
        LEFT JOIN drivers d ON bs.driver_id = d.driver_id
        WHERE r.source LIKE ? AND r.destination LIKE ?
        AND DATE(bs.departure_time) >= ?
        AND b.status IN ('Active')
        AND bs.available_seats > 0
        AND NOT EXISTS (
          SELECT 1 FROM maintenance m
          WHERE m.bus_id = b.bus_id
          AND m.status IN ('Scheduled', 'In Progress')
          AND DATE(bs.departure_time) BETWEEN m.scheduled_date AND COALESCE(m.completion_date, DATE_ADD(m.scheduled_date, INTERVAL 7 DAY))
        )
        ORDER BY bs.departure_time ASC
      `, [`%${source}%`, `%${destination}%`, date]);

      return buses;
    } catch (error) {
      throw error;
    }
  }

  // Get all unique locations from routes
  static async getLocations() {
    try {
      const [locations] = await db.execute(`
        SELECT DISTINCT source as location FROM routes
        UNION
        SELECT DISTINCT destination as location FROM routes
        ORDER BY location ASC
      `);

      return locations.map(row => row.location);
    } catch (error) {
      throw error;
    }
  }

  // Search buses with flexible date range
  static async searchBusesFlexible(source, destination, startDate, endDate = null) {
    try {
      const dateCondition = endDate
        ? 'DATE(bs.departure_time) BETWEEN ? AND ?'
        : 'DATE(bs.departure_time) >= ?';

      const params = endDate
        ? [`%${source}%`, `%${destination}%`, startDate, endDate]
        : [`%${source}%`, `%${destination}%`, startDate];

      const [buses] = await db.execute(`
        SELECT bs.schedule_id, b.bus_id, b.bus_number, b.bus_type, b.amenities,
               r.source, r.destination, r.distance,
               bs.departure_time, bs.arrival_time, bs.fare, bs.available_seats,
               d.full_name as driver_name,
               DATE(bs.departure_time) as journey_date
        FROM bus_schedules bs
        JOIN buses b ON bs.bus_id = b.bus_id
        JOIN routes r ON bs.route_id = r.route_id
        LEFT JOIN drivers d ON bs.driver_id = d.driver_id
        WHERE r.source LIKE ? AND r.destination LIKE ?
        AND ${dateCondition}
        AND b.status = 'Active'
        AND bs.available_seats > 0
        AND NOT EXISTS (
          SELECT 1 FROM maintenance m
          WHERE m.bus_id = b.bus_id
          AND m.status IN ('Scheduled', 'In Progress')
          AND DATE(bs.departure_time) BETWEEN m.scheduled_date AND COALESCE(m.completion_date, DATE_ADD(m.scheduled_date, INTERVAL 7 DAY))
        )
        ORDER BY bs.departure_time ASC
      `, params);

      return buses;
    } catch (error) {
      throw error;
    }
  }
  // Get all unique locations from routes
  static async getLocations() {
    try {
      const [locations] = await db.execute(`
        SELECT DISTINCT source as location FROM routes
        UNION
        SELECT DISTINCT destination as location FROM routes
        ORDER BY location ASC
      `);

      return locations.map(row => row.location);
    } catch (error) {
      throw error;
    }
  }

  // Search buses with flexible date range
  static async searchBusesFlexible(source, destination, startDate, endDate = null) {
    try {
      const dateCondition = endDate
        ? 'DATE(bs.departure_time) BETWEEN ? AND ?'
        : 'DATE(bs.departure_time) >= ?';

      const params = endDate
        ? [`%${source}%`, `%${destination}%`, startDate, endDate]
        : [`%${source}%`, `%${destination}%`, startDate];

      const [buses] = await db.execute(`
        SELECT bs.schedule_id, b.bus_id, b.bus_number, b.bus_type, b.amenities,
               r.source, r.destination, r.distance,
               bs.departure_time, bs.arrival_time, bs.fare, bs.available_seats,
               d.full_name as driver_name,
               DATE(bs.departure_time) as journey_date
        FROM bus_schedules bs
        JOIN buses b ON bs.bus_id = b.bus_id
        JOIN routes r ON bs.route_id = r.route_id
        LEFT JOIN drivers d ON bs.driver_id = d.driver_id
        WHERE r.source LIKE ? AND r.destination LIKE ?
        AND ${dateCondition}
        AND b.status = 'Active'
        AND bs.available_seats > 0
        AND NOT EXISTS (
          SELECT 1 FROM maintenance m
          WHERE m.bus_id = b.bus_id
          AND m.status IN ('Scheduled', 'In Progress')
          AND DATE(bs.departure_time) BETWEEN m.scheduled_date AND COALESCE(m.completion_date, DATE_ADD(m.scheduled_date, INTERVAL 7 DAY))
        )
        ORDER BY bs.departure_time ASC
      `, params);

      return buses;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Bus;
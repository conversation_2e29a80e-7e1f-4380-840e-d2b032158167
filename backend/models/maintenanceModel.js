const db = require('../db/connection');

class Maintenance {
  // Get all maintenance records
  static async getAll() {
    try {
      const [records] = await db.execute(`
        SELECT m.*, b.bus_number, b.bus_type
        FROM maintenance m
        JOIN buses b ON m.bus_id = b.bus_id
        ORDER BY m.scheduled_date DESC
      `);
      
      return records;
    } catch (error) {
      throw error;
    }
  }
  
  // Get maintenance by ID
  static async getById(maintenanceId) {
    try {
      const [records] = await db.execute(`
        SELECT m.*, b.bus_number, b.bus_type
        FROM maintenance m
        JOIN buses b ON m.bus_id = b.bus_id
        WHERE m.maintenance_id = ?
      `, [maintenanceId]);
      
      if (records.length === 0) {
        return null;
      }
      
      return records[0];
    } catch (error) {
      throw error;
    }
  }
  
  // Create new maintenance record
  static async create(busId, maintenanceType, description, scheduledDate) {
    try {
      const [result] = await db.execute(
        `INSERT INTO maintenance 
         (bus_id, maintenance_type, description, scheduled_date, status) 
         VALUES (?, ?, ?, ?, 'Scheduled')`,
        [busId, maintenanceType, description, scheduledDate]
      );
      
      // Update bus status to Maintenance
      await db.execute(
        'UPDATE buses SET status = ? WHERE bus_id = ?',
        ['Maintenance', busId]
      );
      
      return { 
        maintenanceId: result.insertId, 
        busId, 
        maintenanceType, 
        description, 
        scheduledDate,
        status: 'Scheduled'
      };
    } catch (error) {
      throw error;
    }
  }
  
  // Update maintenance record
  static async update(maintenanceId, status, completionDate, cost) {
    try {
      const connection = await db.getConnection();
      
      try {
        await connection.beginTransaction();
        
        // Get maintenance record
        const [records] = await connection.execute(
          'SELECT * FROM maintenance WHERE maintenance_id = ?',
          [maintenanceId]
        );
        
        if (records.length === 0) {
          throw new Error('Maintenance record not found');
        }
        
        const record = records[0];
        
        // Update maintenance record
        await connection.execute(
          `UPDATE maintenance 
           SET status = ?, completion_date = ?, cost = ?
           WHERE maintenance_id = ?`,
          [status, completionDate, cost, maintenanceId]
        );
        
        // If status is Completed, update bus status to Active
        if (status === 'Completed') {
          await connection.execute(
            'UPDATE buses SET status = ? WHERE bus_id = ?',
            ['Active', record.bus_id]
          );
        }
        
        await connection.commit();
        
        return { 
          maintenanceId, 
          busId: record.bus_id, 
          status, 
          completionDate, 
          cost 
        };
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
    } catch (error) {
      throw error;
    }
  }
  
  // Delete maintenance record
  static async delete(maintenanceId) {
    try {
      await db.execute('DELETE FROM maintenance WHERE maintenance_id = ?', [maintenanceId]);
      return true;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Maintenance;
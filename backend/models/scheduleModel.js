const db = require('../db/connection');

class Schedule {
  // Get all schedules
  static async getAll() {
    try {
      const [schedules] = await db.execute(`
        SELECT bs.*, b.bus_number, b.bus_type, 
               r.source, r.destination, r.distance,
               d.full_name as driver_name
        FROM bus_schedules bs
        JOIN buses b ON bs.bus_id = b.bus_id
        JOIN routes r ON bs.route_id = r.route_id
        LEFT JOIN drivers d ON bs.driver_id = d.driver_id
        ORDER BY bs.departure_time
      `);
      
      return schedules;
    } catch (error) {
      throw error;
    }
  }
  
  // Get schedule by ID
  static async getById(scheduleId) {
    try {
      const [schedules] = await db.execute(`
        SELECT bs.*, b.bus_number, b.bus_type, b.amenities,
               r.source, r.destination, r.distance,
               d.full_name as driver_name
        FROM bus_schedules bs
        JOIN buses b ON bs.bus_id = b.bus_id
        JOIN routes r ON bs.route_id = r.route_id
        LEFT JOIN drivers d ON bs.driver_id = d.driver_id
        WHERE bs.schedule_id = ?
      `, [scheduleId]);

      if (schedules.length === 0) {
        return null;
      }

      return schedules[0];
    } catch (error) {
      throw error;
    }
  }

  // Search schedules by source, destination, and date
  static async search(source, destination, journeyDate) {
    try {
      const [schedules] = await db.execute(`
        SELECT bs.*, b.bus_number, b.bus_type, b.total_seats, b.amenities,
               r.source, r.destination, r.distance,
               d.full_name as driver_name
        FROM bus_schedules bs
        JOIN buses b ON bs.bus_id = b.bus_id
        JOIN routes r ON bs.route_id = r.route_id
        LEFT JOIN drivers d ON bs.driver_id = d.driver_id
        WHERE r.source = ?
        AND r.destination = ?
        AND DATE(bs.departure_time) = ?
        AND bs.available_seats > 0
        AND b.status = 'Active'
        ORDER BY bs.departure_time
      `, [source, destination, journeyDate]);

      return schedules;
    } catch (error) {
      throw error;
    }
  }
  
  // Create new schedule
  static async create(busId, routeId, driverId, departureTime, arrivalTime, fare) {
    try {
      // Get total seats from bus
      const [buses] = await db.execute('SELECT total_seats FROM buses WHERE bus_id = ?', [busId]);
      
      if (buses.length === 0) {
        throw new Error('Bus not found');
      }
      
      const totalSeats = buses[0].total_seats;
      
      const [result] = await db.execute(
        `INSERT INTO bus_schedules 
         (bus_id, route_id, driver_id, departure_time, arrival_time, fare, available_seats) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [busId, routeId, driverId, departureTime, arrivalTime, fare, totalSeats]
      );
      
      return { 
        scheduleId: result.insertId, 
        busId, 
        routeId, 
        driverId, 
        departureTime, 
        arrivalTime, 
        fare, 
        availableSeats: totalSeats 
      };
    } catch (error) {
      throw error;
    }
  }
  
  // Update schedule
  static async update(scheduleId, busId, routeId, driverId, departureTime, arrivalTime, fare) {
    try {
      await db.execute(
        `UPDATE bus_schedules 
         SET bus_id = ?, route_id = ?, driver_id = ?, 
             departure_time = ?, arrival_time = ?, fare = ?
         WHERE schedule_id = ?`,
        [busId, routeId, driverId, departureTime, arrivalTime, fare, scheduleId]
      );
      
      return { 
        scheduleId, 
        busId, 
        routeId, 
        driverId, 
        departureTime, 
        arrivalTime, 
        fare 
      };
    } catch (error) {
      throw error;
    }
  }
  
  // Delete schedule
  static async delete(scheduleId) {
    try {
      await db.execute('DELETE FROM bus_schedules WHERE schedule_id = ?', [scheduleId]);
      return true;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Schedule;
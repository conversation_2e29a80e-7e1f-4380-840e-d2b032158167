{"name": "bus-reservation-system", "version": "1.0.0", "description": "Bus Reservation System Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup-database.js", "setup-dev": "npm run setup && npm run dev", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-session": "^1.17.3", "mysql2": "^3.2.0"}, "devDependencies": {"nodemon": "^3.1.10"}}
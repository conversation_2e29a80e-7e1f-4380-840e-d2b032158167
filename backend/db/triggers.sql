-- Triggers for Bus Reservation System
USE bus_reservation_system;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS after_booking_insert;
DROP TRIGGER IF EXISTS after_booking_update;
DROP TRIGGER IF EXISTS after_bus_status_change;
DROP TRIGGER IF EXISTS check_maintenance_after_booking;

-- Trigger to update available seats when a booking is confirmed
DELIMITER //
CREATE TRIGGER after_booking_insert
AFTER INSERT ON bookings
FOR EACH ROW
BEGIN
    -- Update available seats in bus_schedules
    UPDATE bus_schedules
    SET available_seats = available_seats - NEW.total_seats
    WHERE schedule_id = NEW.schedule_id;
    
    -- Log in booking history
    INSERT INTO booking_history (booking_id, user_id, schedule_id, action, details)
    VALUES (NEW.booking_id, NEW.user_id, NEW.schedule_id, 'CREATED', 
            CONCAT('Booking created with ', NEW.total_seats, ' seats'));
END //
DELIMITER ;

-- Trigger to update available seats when a booking is cancelled
DELIMITER //
CREATE TRIGGER after_booking_update
AFTER UPDATE ON bookings
FOR EACH ROW
BEGIN
    IF NEW.booking_status = 'Cancelled' AND OLD.booking_status != 'Cancelled' THEN
        -- Restore available seats in bus_schedules
        UPDATE bus_schedules
        SET available_seats = available_seats + NEW.total_seats
        WHERE schedule_id = NEW.schedule_id;
        
        -- Log in booking history
        INSERT INTO booking_history (booking_id, user_id, schedule_id, action, details)
        VALUES (NEW.booking_id, NEW.user_id, NEW.schedule_id, 'CANCELLED', 
                CONCAT('Booking cancelled, ', NEW.total_seats, ' seats released'));
    END IF;
END //
DELIMITER ;

-- Trigger to automatically create maintenance entry when bus status changes to 'Inactive'
DELIMITER //
CREATE TRIGGER after_bus_status_change
AFTER UPDATE ON buses
FOR EACH ROW
BEGIN
    IF NEW.status = 'Inactive' AND OLD.status != 'Inactive' THEN
        INSERT INTO maintenance (bus_id, maintenance_type, description, scheduled_date, status)
        VALUES (NEW.bus_id, 'Routine Check', 'Automatically scheduled due to status change', 
                CURDATE(), 'Scheduled');
    END IF;
END //
DELIMITER ;

-- Trigger to automatically schedule maintenance after 50 bookings for a bus
DELIMITER //
CREATE TRIGGER check_maintenance_after_booking
AFTER INSERT ON bookings
FOR EACH ROW
BEGIN
    DECLARE booking_count INT DEFAULT 0;
    DECLARE bus_id_var INT DEFAULT 0;
    DECLARE last_maintenance_date DATE DEFAULT NULL;
    
    -- Get the bus_id from the schedule
    SELECT bs.bus_id INTO bus_id_var
    FROM bus_schedules bs
    WHERE bs.schedule_id = NEW.schedule_id;
    
    -- Count confirmed bookings for this bus since last maintenance
    SELECT COALESCE(MAX(completion_date), '2020-01-01') INTO last_maintenance_date
    FROM maintenance 
    WHERE bus_id = bus_id_var AND status = 'Completed';
    
    -- Count bookings since last maintenance
    SELECT COUNT(*) INTO booking_count
    FROM bookings b
    JOIN bus_schedules bs ON b.schedule_id = bs.schedule_id
    WHERE bs.bus_id = bus_id_var 
    AND b.booking_status = 'Confirmed'
    AND b.booking_date > last_maintenance_date;
    
    -- If 50 or more bookings, schedule maintenance
    IF booking_count >= 50 THEN
        INSERT INTO maintenance (bus_id, maintenance_type, description, scheduled_date, status)
        VALUES (bus_id_var, 'Scheduled Maintenance', 
                CONCAT('Auto-scheduled after ', booking_count, ' bookings'), 
                DATE_ADD(CURDATE(), INTERVAL 7 DAY), 'Scheduled');
    END IF;
END //
DELIMITER ;

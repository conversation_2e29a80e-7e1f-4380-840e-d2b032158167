USE bus_reservation_system;

-- Insert admin user
INSERT INTO admins (username, password, full_name) VALUES
('admin', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'System Administrator');
-- Password is 'admin123' (hashed)

-- Insert sample buses
INSERT INTO buses (bus_number, bus_type, total_seats, amenities, status) VALUES
('BUS001', 'Luxury', 40, 'AC, WiFi, USB Charging, Reclining Seats', 'Active'),
('BUS002', 'Standard', 50, 'AC, USB Charging', 'Active'),
('BUS003', 'Sleeper', 30, 'AC, WiFi, USB Charging, Sleeper Berths', 'Active'),
('BUS004', 'Deluxe', 45, 'AC, WiFi, Entertainment System', 'Maintenance');

-- Insert sample drivers
INSERT INTO drivers (full_name, license_number, contact_number, experience_years) VALUES
('<PERSON>', '**********', '**********', 8),
('<PERSON>', '**********', '**********', 5),
('<PERSON>', '**********', '**********', 10);

-- Insert sample bus staff
INSERT INTO bus_staff (full_name, role, contact_number) VALUES
('Michael Brown', 'Conductor', '**********'),
('<PERSON> <PERSON>', 'Helper', '**********'),
('David Miller', 'Conductor', '**********');

-- Insert sample routes
INSERT INTO routes (source, destination, distance) VALUES
('New York', 'Washington DC', 225.5),
('Boston', 'New York', 215.2),
('Chicago', 'Detroit', 283.1),
('Los Angeles', 'San Francisco', 383.0),
('Seattle', 'Portland', 174.2);

-- Insert sample bus schedules
INSERT INTO bus_schedules (bus_id, route_id, driver_id, departure_time, arrival_time, fare, available_seats) VALUES
(1, 1, 1, '2023-06-01 08:00:00', '2023-06-01 12:30:00', 45.00, 40),
(2, 2, 2, '2023-06-01 09:30:00', '2023-06-01 13:45:00', 35.00, 50),
(3, 3, 3, '2023-06-01 22:00:00', '2023-06-02 06:00:00', 65.00, 30),
(1, 4, 1, '2023-06-02 07:00:00', '2023-06-02 14:30:00', 75.00, 40),
(2, 5, 2, '2023-06-02 10:00:00', '2023-06-02 13:00:00', 30.00, 50);

-- Insert sample users
INSERT INTO users (email, password, full_name, phone) VALUES
('<EMAIL>', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'User One', '1234567890'),
('<EMAIL>', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'User Two', '2345678901');
-- Password is 'password123' (hashed)

-- Insert sample passengers
INSERT INTO passengers (user_id, full_name, age, gender, contact_number) VALUES
(1, 'User One', 30, 'Male', '1234567890'),
(1, 'Jane One', 28, 'Female', '1234567891'),
(2, 'User Two', 35, 'Male', '2345678901');

-- Insert sample bookings
INSERT INTO bookings (user_id, schedule_id, journey_date, seat_numbers, total_seats, total_fare, booking_status) VALUES
(1, 1, '2023-06-01', 'A1,A2', 2, 90.00, 'Confirmed'),
(2, 3, '2023-06-01', 'B5,B6,B7', 3, 195.00, 'Confirmed');

-- Insert sample payments
INSERT INTO payments (booking_id, amount, payment_method, transaction_id, payment_status) VALUES
(1, 90.00, 'Credit Card', 'TXN123456789', 'Success'),
(2, 195.00, 'PayPal', 'TXN987654321', 'Success');

-- Insert sample feedback
INSERT INTO feedback (user_id, booking_id, rating, comments) VALUES
(1, 1, 4, 'Great service, comfortable journey.'),
(2, 2, 5, 'Excellent experience, very punctual.');

-- Insert sample maintenance records
INSERT INTO maintenance (bus_id, maintenance_type, description, scheduled_date, completion_date, status, cost) VALUES
(4, 'Engine Overhaul', 'Regular engine maintenance', '2023-05-25', NULL, 'In Progress', 1500.00),
(2, 'Tire Replacement', 'Replace all tires', '2023-06-10', NULL, 'Scheduled', 800.00);
-- Sample data for bus reservation system
USE bus_reservation_system;

-- Insert sample buses if they don't exist
INSERT IGNORE INTO buses (bus_id, bus_number, bus_type, total_seats, amenities, status) VALUES
(1, 'BUS001', 'Luxury', 40, 'AC, WiFi, USB Charging, Reclining Seats', 'Active'),
(2, 'BUS002', 'Standard', 50, 'AC, USB Charging', 'Active'),
(3, 'BUS003', 'Sleeper', 30, 'AC, WiFi, USB Charging, Sleeper Berths', 'Active'),
(4, 'BUS004', 'Deluxe', 45, 'AC, WiFi, Entertainment System', 'Active'),
(5, 'BUS005', 'Standard', 50, 'AC, USB Charging', 'Active');

-- Insert sample routes if they don't exist
INSERT IGNORE INTO routes (route_id, source, destination, distance) VALUES
(1, 'New York', 'Washington DC', 225.5),
(2, 'Boston', 'New York', 215.3),
(3, 'Chicago', 'Detroit', 283.7),
(4, 'Los Angeles', 'San Francisco', 382.1),
(5, 'Miami', 'Orlando', 235.8);

-- Insert sample drivers if they don't exist
INSERT IGNORE INTO drivers (driver_id, full_name, license_number, phone, status) VALUES
(1, '<PERSON>', 'DL123456789', '555-0101', 'Active'),
(2, 'Mike Johnson', 'DL987654321', '555-0102', 'Active'),
(3, 'David <PERSON>', 'DL456789123', '555-0103', 'Active'),
(4, 'Robert Wilson', 'DL789123456', '555-0104', 'Active'),
(5, 'James Davis', 'DL321654987', '555-0105', 'Active');

-- Insert sample schedules if they don't exist
INSERT IGNORE INTO bus_schedules (schedule_id, bus_id, route_id, driver_id, departure_time, arrival_time, fare, available_seats) VALUES
(1, 1, 1, 1, '2024-01-15 08:00:00', '2024-01-15 12:30:00', 45.00, 35),
(2, 2, 2, 2, '2024-01-15 09:00:00', '2024-01-15 13:15:00', 40.00, 45),
(3, 3, 3, 3, '2024-01-15 10:00:00', '2024-01-15 15:30:00', 65.00, 25),
(4, 4, 4, 4, '2024-01-15 11:00:00', '2024-01-15 17:45:00', 85.00, 40),
(5, 5, 5, 5, '2024-01-15 12:00:00', '2024-01-15 16:30:00', 35.00, 48),
-- Future dates
(6, 1, 1, 1, '2024-01-20 08:00:00', '2024-01-20 12:30:00', 45.00, 40),
(7, 2, 2, 2, '2024-01-20 09:00:00', '2024-01-20 13:15:00', 40.00, 50),
(8, 3, 3, 3, '2024-01-20 10:00:00', '2024-01-20 15:30:00', 65.00, 30),
(9, 4, 4, 4, '2024-01-25 11:00:00', '2024-01-25 17:45:00', 85.00, 45),
(10, 5, 5, 5, '2024-01-25 12:00:00', '2024-01-25 16:30:00', 35.00, 50);

-- Insert sample admin if not exists
INSERT IGNORE INTO admins (admin_id, username, password, full_name) VALUES
(1, 'admin', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'System Administrator');
-- Password is 'admin123' (hashed)

-- Insert sample users if they don't exist
INSERT IGNORE INTO users (user_id, email, password, full_name, phone) VALUES
(1, '<EMAIL>', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'John Doe', '1234567890'),
(2, '<EMAIL>', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'Jane Smith', '2345678901'),
(3, '<EMAIL>', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'Test User', '3456789012');
-- Password is 'password123' (hashed)

-- Insert sample bookings if they don't exist
INSERT IGNORE INTO bookings (booking_id, user_id, schedule_id, journey_date, seat_numbers, total_seats, total_fare, booking_status) VALUES
(1, 1, 1, '2024-01-15', 'A1,A2', 2, 90.00, 'Confirmed'),
(2, 1, 3, '2024-01-20', 'B5,B6,B7', 3, 195.00, 'Confirmed'),
(3, 2, 2, '2024-01-15', 'C1', 1, 40.00, 'Confirmed'),
(4, 3, 4, '2024-01-25', 'D1,D2', 2, 170.00, 'Confirmed');

-- Insert sample payments if they don't exist
INSERT IGNORE INTO payments (payment_id, booking_id, amount, payment_method, transaction_id, payment_status) VALUES
(1, 1, 90.00, 'Credit Card', 'TXN123456789', 'Success'),
(2, 2, 195.00, 'PayPal', 'TXN987654321', 'Success'),
(3, 3, 40.00, 'Credit Card', 'TXN456789123', 'Success'),
(4, 4, 170.00, 'Debit Card', 'TXN789123456', 'Success');

-- Insert sample feedback if it doesn't exist
INSERT IGNORE INTO feedback (feedback_id, user_id, booking_id, rating, comments) VALUES
(1, 1, 1, 4, 'Great service, comfortable journey.'),
(2, 1, 2, 5, 'Excellent experience, very punctual.'),
(3, 2, 3, 3, 'Good service but could be improved.'),
(4, 3, 4, 5, 'Outstanding service, highly recommended!');

-- Insert sample passengers if they don't exist
INSERT IGNORE INTO passengers (passenger_id, user_id, full_name, age, gender, contact_number) VALUES
(1, 1, 'John Doe', 30, 'Male', '1234567890'),
(2, 1, 'Jane Doe', 28, 'Female', '1234567891'),
(3, 2, 'Jane Smith', 35, 'Female', '2345678901'),
(4, 3, 'Test User', 25, 'Other', '3456789012');

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function setupSampleData() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || 'password',
      database: process.env.DB_NAME || 'bus_reservation_system',
      multipleStatements: true
    });

    console.log('Connected to database');

    // Execute sample data step by step
    console.log('Inserting sample data...');

    // Insert buses
    console.log('Inserting buses...');
    await connection.execute(`
      INSERT IGNORE INTO buses (bus_id, bus_number, bus_type, total_seats, amenities, status) VALUES
      (1, 'BUS001', 'Luxury', 40, 'AC, WiFi, USB Charging, Reclining Seats', 'Active'),
      (2, 'BUS002', 'Standard', 50, 'AC, USB Charging', 'Active'),
      (3, 'BUS003', 'Sleeper', 30, 'AC, WiFi, USB Charging, Sleeper Berths', 'Active'),
      (4, 'BUS004', 'Deluxe', 45, 'AC, WiFi, Entertainment System', 'Active'),
      (5, 'BUS005', 'Standard', 50, 'AC, USB Charging', 'Active')
    `);

    // Insert routes
    console.log('Inserting routes...');
    await connection.execute(`
      INSERT IGNORE INTO routes (route_id, source, destination, distance) VALUES
      (1, 'New York', 'Washington DC', 225.5),
      (2, 'Boston', 'New York', 215.3),
      (3, 'Chicago', 'Detroit', 283.7),
      (4, 'Los Angeles', 'San Francisco', 382.1),
      (5, 'Miami', 'Orlando', 235.8)
    `);

    // Insert drivers
    console.log('Inserting drivers...');
    await connection.execute(`
      INSERT IGNORE INTO drivers (driver_id, full_name, license_number, contact_number, experience_years) VALUES
      (1, 'John Smith', 'DL123456789', '555-0101', 5),
      (2, 'Mike Johnson', 'DL987654321', '555-0102', 8),
      (3, 'David Brown', 'DL456789123', '555-0103', 3),
      (4, 'Robert Wilson', 'DL789123456', '555-0104', 10),
      (5, 'James Davis', 'DL321654987', '555-0105', 7)
    `);

    // Insert schedules
    console.log('Inserting schedules...');
    await connection.execute(`
      INSERT IGNORE INTO bus_schedules (schedule_id, bus_id, route_id, driver_id, departure_time, arrival_time, fare, available_seats) VALUES
      (1, 1, 1, 1, '2024-01-15 08:00:00', '2024-01-15 12:30:00', 45.00, 35),
      (2, 2, 2, 2, '2024-01-15 09:00:00', '2024-01-15 13:15:00', 40.00, 45),
      (3, 3, 3, 3, '2024-01-15 10:00:00', '2024-01-15 15:30:00', 65.00, 25),
      (4, 4, 4, 4, '2024-01-15 11:00:00', '2024-01-15 17:45:00', 85.00, 40),
      (5, 5, 5, 5, '2024-01-15 12:00:00', '2024-01-15 16:30:00', 35.00, 48)
    `);

    // Insert users
    console.log('Inserting users...');
    await connection.execute(`
      INSERT IGNORE INTO users (user_id, email, password, full_name, phone) VALUES
      (1, '<EMAIL>', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'John Doe', '1234567890'),
      (2, '<EMAIL>', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'Jane Smith', '2345678901'),
      (3, '<EMAIL>', '$2b$10$rQnV0YL.Bh/jQj.DCYJ8.uRfBKrLIrMhkVl9N8hBDvNs7LaEUJKGa', 'Test User', '3456789012')
    `);

    // Insert bookings
    console.log('Inserting bookings...');
    await connection.execute(`
      INSERT IGNORE INTO bookings (booking_id, user_id, schedule_id, journey_date, seat_numbers, total_seats, total_fare, booking_status) VALUES
      (1, 1, 1, '2024-01-15', 'A1,A2', 2, 90.00, 'Confirmed'),
      (2, 1, 3, '2024-01-20', 'B5,B6,B7', 3, 195.00, 'Confirmed'),
      (3, 2, 2, '2024-01-15', 'C1', 1, 40.00, 'Confirmed'),
      (4, 3, 4, '2024-01-25', 'D1,D2', 2, 170.00, 'Confirmed')
    `);

    // Insert payments
    console.log('Inserting payments...');
    await connection.execute(`
      INSERT IGNORE INTO payments (payment_id, booking_id, amount, payment_method, transaction_id, payment_status) VALUES
      (1, 1, 90.00, 'Credit Card', 'TXN123456789', 'Success'),
      (2, 2, 195.00, 'PayPal', 'TXN987654321', 'Success'),
      (3, 3, 40.00, 'Credit Card', 'TXN456789123', 'Success'),
      (4, 4, 170.00, 'Debit Card', 'TXN789123456', 'Success')
    `);
    
    console.log('Sample data inserted successfully!');
    
    // Verify data
    const [buses] = await connection.execute('SELECT COUNT(*) as count FROM buses');
    const [routes] = await connection.execute('SELECT COUNT(*) as count FROM routes');
    const [schedules] = await connection.execute('SELECT COUNT(*) as count FROM bus_schedules');
    const [bookings] = await connection.execute('SELECT COUNT(*) as count FROM bookings');
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
    
    console.log('\nData verification:');
    console.log(`Buses: ${buses[0].count}`);
    console.log(`Routes: ${routes[0].count}`);
    console.log(`Schedules: ${schedules[0].count}`);
    console.log(`Bookings: ${bookings[0].count}`);
    console.log(`Users: ${users[0].count}`);
    
  } catch (error) {
    console.error('Error setting up sample data:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\nDatabase connection closed');
    }
  }
}

// Run the setup
setupSampleData();

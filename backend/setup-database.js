const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function setupDatabase() {
  let connection;

  try {
    console.log('🚀 Starting database setup...');

    // First, create connection without database to create the database
    let tempConnection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || 'password'
    });

    // Create database if it doesn't exist
    await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME || 'bus_reservation_system'}`);
    await tempConnection.end();
    console.log('✅ Database created/verified');

    // Now connect to the specific database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || 'password',
      database: process.env.DB_NAME || 'bus_reservation_system',
      multipleStatements: true
    });

    console.log('✅ Connected to database');

    // Read and execute init.sql (excluding triggers for now)
    const initSqlPath = path.join(__dirname, 'db', 'init.sql');
    if (fs.existsSync(initSqlPath)) {
      const initSql = fs.readFileSync(initSqlPath, 'utf8');

      // Extract only the table creation statements
      const lines = initSql.split('\n');
      let currentStatement = '';
      let inTriggerSection = false;

      for (let line of lines) {
        const trimmedLine = line.trim();

        // Skip trigger and procedure sections
        if (trimmedLine.includes('-- TRIGGERS') || trimmedLine.includes('-- STORED PROCEDURES')) {
          inTriggerSection = true;
          continue;
        }

        if (inTriggerSection) continue;

        // Skip comments and empty lines
        if (trimmedLine.startsWith('--') || trimmedLine === '') continue;

        currentStatement += line + '\n';

        // Execute when we hit a semicolon at the end of a line
        if (trimmedLine.endsWith(';') && !trimmedLine.includes('DELIMITER')) {
          try {
            await connection.execute(currentStatement);
          } catch (error) {
            if (!error.message.includes('already exists')) {
              console.warn('Warning:', error.message);
            }
          }
          currentStatement = '';
        }
      }
      console.log('✅ Database schema initialized');
    }

    // Read and execute sample-data.sql
    const sampleDataPath = path.join(__dirname, 'db', 'sample-data.sql');
    if (fs.existsSync(sampleDataPath)) {
      const sampleDataSql = fs.readFileSync(sampleDataPath, 'utf8');

      // Split into individual statements and execute them one by one
      const statements = sampleDataSql
        .split(';')
        .filter(stmt => {
          const trimmed = stmt.trim();
          return trimmed && !trimmed.startsWith('--') && !trimmed.includes('USE ');
        });

      for (let statement of statements) {
        if (statement.trim()) {
          try {
            await connection.execute(statement.trim() + ';');
          } catch (error) {
            if (!error.message.includes('already exists') &&
                !error.message.includes("doesn't exist")) {
              console.warn('Warning inserting data:', error.message);
            }
          }
        }
      }
      console.log('✅ Sample data inserted');
    }

    console.log('🎉 Database setup completed successfully!');
    console.log('');
    console.log('📋 Default credentials:');
    console.log('   Admin: username=admin, password=admin123');
    console.log('   User: email=<EMAIL>, password=password123');
    console.log('');

  } catch (error) {
    console.error('❌ Error setting up database:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = setupDatabase;

# 🚌 Bus Reservation System

A comprehensive, production-ready bus reservation system with user and admin modules, featuring real-time booking, seat selection, and automated maintenance scheduling.

## ✨ Features

### 👤 User Module
- ✅ **User Registration & Login** - Secure session-based authentication
- 🔍 **Smart Bus Search** - Search by source, destination, and date
- 🎫 **Interactive Seat Selection** - Visual seat map with real-time availability
- 📋 **Booking Management** - View history, upcoming trips, and cancellations
- ⭐ **Feedback System** - Rate and review your journey experience
- 💳 **Secure Payments** - Integrated payment processing with confirmation

### 🧑‍💼 Admin Module
- 🔐 **Admin Dashboard** - Comprehensive system overview with statistics
- 🚌 **Fleet Management** - Add, edit, and manage bus inventory
- 🛣️ **Route Management** - Create and manage source-destination routes
- ⏰ **Schedule Management** - Set departure times, fares, and availability
- 📊 **Booking Analytics** - View all bookings with filtering and reporting
- 💬 **Feedback Monitoring** - Track customer satisfaction and reviews
- 🔧 **Maintenance Tracking** - Automated and manual maintenance scheduling
- 💰 **Revenue Analytics** - Real-time revenue tracking and reporting

### ⚙️ System Features
- 🏗️ **REST API** - Built with Node.js + Express.js
- 🗄️ **MySQL Database** - Normalized database design (3NF)
- 🔄 **Auto-Maintenance** - Triggers maintenance after 50 bookings per bus
- 📱 **Responsive Design** - Works on desktop, tablet, and mobile
- 🔒 **Security** - Password hashing, session management, SQL injection protection
- 📈 **Real-time Updates** - Live seat availability and booking confirmations

## 🛠️ Tech Stack

### Backend
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL 8.0+
- **Authentication**: Session-based with bcrypt
- **ORM**: Native MySQL2 with prepared statements

### Frontend
- **Core**: HTML5, CSS3, ES6+ JavaScript
- **Styling**: Custom CSS with CSS Grid and Flexbox
- **Icons**: Emoji-based for universal compatibility
- **API**: Fetch API for backend communication

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- MySQL 8.0+
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bus-reservation-system
   ```

2. **Install dependencies**
   ```bash
   cd backend
   npm install
   ```

3. **Configure environment**
   ```bash
   # Copy and edit the environment file
   cp .env.example .env
   ```

   Edit `.env` with your database credentials:
   ```env
   PORT=8080
   DB_HOST=localhost
   DB_USER=root
   DB_PASS=your_password
   DB_NAME=bus_reservation_system
   SESSION_SECRET=your_session_secret_key
   ```

4. **Setup database**
   ```bash
   # Automated database setup
   node setup-database.js
   ```

5. **Start the application**
   ```bash
   # Start backend server
   npm start

   # In another terminal, start frontend server
   cd ../frontend
   python -m http.server 3000
   ```

6. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080/api

## 🔑 Default Credentials

### Admin Access
- **Username**: `admin`
- **Password**: `admin123`
- **URL**: http://localhost:3000/admin-login.html

### Test User Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **URL**: http://localhost:3000/user-login.html

## 📁 Project Structure

```
bus-reservation-system/
├── backend/
│   ├── controllers/          # Request handlers
│   ├── models/              # Database models
│   ├── routes/              # API routes
│   ├── middleware/          # Authentication & validation
│   ├── db/                  # Database scripts
│   │   ├── init.sql         # Schema & triggers
│   │   ├── sample-data.sql  # Sample data
│   │   └── connection.js    # Database connection
│   ├── setup-database.js    # Automated setup script
│   ├── server.js           # Main server file
│   └── package.json        # Dependencies
├── frontend/
│   ├── css/                # Stylesheets
│   ├── js/                 # JavaScript modules
│   ├── *.html             # Application pages
│   └── assets/            # Images & resources
└── README.md              # This file
```

## 🔧 API Endpoints

### Authentication
- `POST /api/users/register` - User registration
- `POST /api/users/login` - User login
- `POST /api/admin/login` - Admin login
- `POST /api/users/logout` - Logout

### Booking System
- `GET /api/buses/search` - Search available buses
- `POST /api/bookings` - Create new booking
- `GET /api/bookings/user` - Get user bookings
- `PUT /api/bookings/:id/cancel` - Cancel booking

### Admin Management
- `GET /api/admin/dashboard-stats` - Dashboard statistics
- `GET /api/buses` - Manage buses
- `GET /api/routes` - Manage routes
- `GET /api/maintenance` - Maintenance records

## 🗄️ Database Schema

### Key Tables
- **users** - User accounts and profiles
- **admins** - Administrator accounts
- **buses** - Bus fleet information
- **routes** - Source-destination pairs
- **bus_schedules** - Timetables and pricing
- **bookings** - Ticket reservations
- **payments** - Payment records
- **feedback** - Customer reviews
- **maintenance** - Service records

### Automated Features
- **Seat Management**: Automatic seat allocation/deallocation
- **Maintenance Scheduling**: Auto-trigger after 50 bookings
- **Booking History**: Complete audit trail
- **Revenue Tracking**: Real-time financial reporting

## 🚀 Deployment

### Production Setup
1. **Environment Configuration**
   ```bash
   NODE_ENV=production
   PORT=8080
   DB_HOST=your-production-db-host
   DB_USER=your-db-user
   DB_PASS=your-secure-password
   SESSION_SECRET=your-strong-session-secret
   ```

2. **Database Optimization**
   - Enable MySQL query caching
   - Set up database backups
   - Configure connection pooling

3. **Security Enhancements**
   - Use HTTPS in production
   - Implement rate limiting
   - Add CORS restrictions
   - Enable security headers

### Docker Deployment (Optional)
```dockerfile
# Dockerfile example
FROM node:16-alpine
WORKDIR /app
COPY backend/package*.json ./
RUN npm ci --only=production
COPY backend/ .
EXPOSE 8080
CMD ["node", "server.js"]
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] User registration and login
- [ ] Bus search functionality
- [ ] Seat selection and booking
- [ ] Payment processing
- [ ] Booking cancellation
- [ ] Admin dashboard access
- [ ] Bus/route management
- [ ] Maintenance scheduling
- [ ] Feedback submission

### API Testing
```bash
# Test API endpoints
curl -X GET http://localhost:8080/api/test
curl -X POST http://localhost:8080/api/users/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password","fullName":"Test User"}'
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Common Issues

**Database Connection Error**
- Verify MySQL is running
- Check credentials in `.env` file
- Ensure database exists

**Port Already in Use**
- Change PORT in `.env` file
- Kill existing processes: `lsof -ti:8080 | xargs kill`

**Frontend Not Loading**
- Ensure Python HTTP server is running
- Check CORS settings in backend
- Verify API_BASE_URL in frontend

### Getting Help
- 📧 Email: <EMAIL>
- 💬 Issues: Create a GitHub issue
- 📖 Documentation: Check the wiki

---

**Made with ❤️ for efficient bus travel management**